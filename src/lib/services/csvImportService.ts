import { invoke } from '@tauri-apps/api/core';
import { save } from '@tauri-apps/plugin-dialog';
import { writeTextFile } from '@tauri-apps/plugin-fs';

// CSV导入相关接口定义

export interface CsvPersonnelRow {
  project_name: string;
  authorized_personnel: string;
  row_number: number;
}

export interface ParsedPersonnelRole {
  name: string;
  role_name: string;
  original_string: string;
}

export interface ProjectMatch {
  project_id: string;
  project_short_name: string;
  project_name: string;
  match_type: string;
}

export interface PersonnelMatch {
  personnel_id: number;
  name: string;
  exact_match: boolean;
}

export interface RoleMatch {
  role_item_id: number;
  role_name: string;
  exact_match: boolean;
}

export interface PersonnelRoleAssignment {
  personnel_match: PersonnelMatch;
  role_match: RoleMatch;
  parsed_role: ParsedPersonnelRole;
  already_exists: boolean;
}

export interface CsvImportRecord {
  row_number: number;
  project_match: ProjectMatch;
  personnel_assignments: PersonnelRoleAssignment[];
  original_row: CsvPersonnelRow;
}

export interface CsvImportError {
  error_type: 'ProjectNotFound' | 'PersonnelNotFound' | 'RoleNotFound' | 'PersonnelParsingError' | 'DataFormatError' | 'DatabaseError';
  row_number: number;
  message: string;
  context?: string;
}

export interface CsvImportWarning {
  warning_type: 'FuzzyMatch' | 'DuplicateAssignment' | 'MissingCriticalRole';
  row_number: number;
  message: string;
  context?: string;
}

export interface CsvImportStatistics {
  total_rows: number;
  successful_rows: number;
  error_rows: number;
  warning_rows: number;
  total_assignments: number;
  new_assignments: number;
  duplicate_assignments: number;
  affected_projects: number;
}

export interface CsvImportValidation {
  valid_records: CsvImportRecord[];
  errors: CsvImportError[];
  warnings: CsvImportWarning[];
  statistics: CsvImportStatistics;
}

export interface CsvImportResult {
  success: boolean;
  imported_records: number;
  skipped_records: number;
  errors: string[];
  warnings: string[];
  statistics: CsvImportStatistics;
}

export interface QualityControlResult {
  project_id: string;
  project_name: string;
  missing_critical_roles: string[];
  quality_passed: boolean;
  recommendations: string[];
}

export interface ImportHistoryRecord {
  id: number;
  import_time: string;
  file_name: string;
  total_records: number;
  successful_records: number;
  failed_records: number;
  status: string;
}

export interface PersonnelStatistics {
  total_assignments: number;
  unique_personnel: number;
  unique_roles: number;
}

/**
 * CSV导入服务类
 */
export class CsvImportService {
  private dbPath: string;

  constructor() {
    this.dbPath = '/Users/<USER>/我的文档/sqlite/peckbyte.db';
  }

  /**
   * 解析并验证CSV文件内容
   * @param csvContent CSV文件内容
   * @returns 验证结果
   */
  async parseCsvPersonnelData(csvContent: string): Promise<CsvImportValidation> {
    try {
      console.log('开始解析CSV人员数据');
      const result = await invoke<CsvImportValidation>('parse_csv_personnel_data', {
        csvContent,
        dbPath: this.dbPath
      });
      console.log('CSV解析完成:', result.statistics);
      return result;
    } catch (error: any) {
      console.error('CSV解析失败:', error);
      throw new Error(`CSV解析失败: ${error}`);
    }
  }

  /**
   * 执行CSV导入
   * @param validation 验证结果
   * @returns 导入结果
   */
  async executeCsvPersonnelImport(validation: CsvImportValidation): Promise<CsvImportResult> {
    try {
      console.log('开始执行CSV人员导入');
      const result = await invoke<CsvImportResult>('execute_csv_personnel_import', {
        validation,
        dbPath: this.dbPath
      });
      console.log('CSV导入完成:', result);
      return result;
    } catch (error: any) {
      console.error('CSV导入失败:', error);
      throw new Error(`CSV导入失败: ${error}`);
    }
  }

  /**
   * 执行项目质量控制检查
   * @param projectIds 项目ID列表（可选）
   * @returns 质量控制结果
   */
  async performProjectQualityControl(projectIds?: string[]): Promise<QualityControlResult[]> {
    try {
      console.log('开始执行项目质量控制检查');
      const result = await invoke<QualityControlResult[]>('perform_project_quality_control', {
        projectIds: projectIds || null,
        dbPath: this.dbPath
      });
      console.log('质量控制检查完成:', result);
      return result;
    } catch (error: any) {
      console.error('质量控制检查失败:', error);
      throw new Error(`质量控制检查失败: ${error}`);
    }
  }

  /**
   * 获取CSV导入模板
   * @returns CSV模板内容
   */
  async getCsvImportTemplate(): Promise<string> {
    try {
      console.log('获取CSV导入模板');
      const result = await invoke<string>('get_csv_import_template');
      return result;
    } catch (error: any) {
      console.error('获取CSV模板失败:', error);
      throw new Error(`获取CSV模板失败: ${error}`);
    }
  }

  /**
   * 验证CSV格式
   * @param csvContent CSV内容
   * @returns 是否格式正确
   */
  async validateCsvFormat(csvContent: string): Promise<boolean> {
    try {
      console.log('验证CSV格式');
      const result = await invoke<boolean>('validate_csv_format', {
        csvContent
      });
      return result;
    } catch (error: any) {
      console.error('CSV格式验证失败:', error);
      throw new Error(`CSV格式验证失败: ${error}`);
    }
  }

  /**
   * 获取导入历史记录
   * @param limit 记录数限制
   * @returns 历史记录列表
   */
  async getImportHistory(limit?: number): Promise<ImportHistoryRecord[]> {
    try {
      console.log('获取导入历史记录');
      const result = await invoke<ImportHistoryRecord[]>('get_import_history', {
        limit: limit || null,
        dbPath: this.dbPath
      });
      return result;
    } catch (error: any) {
      console.error('获取导入历史失败:', error);
      throw new Error(`获取导入历史失败: ${error}`);
    }
  }

  /**
   * 批量删除人员角色分配
   * @param projectId 项目ID
   * @param assignmentIds 分配ID列表
   * @returns 删除的记录数
   */
  async batchDeletePersonnelAssignments(projectId: string, assignmentIds: number[]): Promise<number> {
    try {
      console.log('批量删除人员角色分配:', { projectId, assignmentIds });
      const result = await invoke<number>('batch_delete_personnel_assignments', {
        projectId,
        assignmentIds,
        dbPath: this.dbPath
      });
      console.log('批量删除完成，删除记录数:', result);
      return result;
    } catch (error: any) {
      console.error('批量删除失败:', error);
      throw new Error(`批量删除失败: ${error}`);
    }
  }

  /**
   * 获取项目人员统计信息
   * @param projectId 项目ID（可选）
   * @returns 统计信息
   */
  async getProjectPersonnelStatistics(projectId?: string): Promise<PersonnelStatistics> {
    try {
      console.log('获取项目人员统计信息');
      const result = await invoke<PersonnelStatistics>('get_project_personnel_statistics', {
        projectId: projectId || null,
        dbPath: this.dbPath
      });
      return result;
    } catch (error: any) {
      console.error('获取统计信息失败:', error);
      throw new Error(`获取统计信息失败: ${error}`);
    }
  }

  /**
   * 读取文件内容
   * @param file 文件对象
   * @returns 文件内容
   */
  async readFileContent(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e) => {
        const content = e.target?.result as string;
        resolve(content);
      };
      reader.onerror = () => {
        reject(new Error('文件读取失败'));
      };
      reader.readAsText(file, 'utf-8');
    });
  }

  /**
   * 下载CSV模板文件
   */
  async downloadCsvTemplate(): Promise<void> {
    try {
      console.log('开始获取CSV模板...');
      const template = await this.getCsvImportTemplate();
      console.log('获取到模板内容，长度:', template.length);

      // 添加BOM以确保Excel正确识别UTF-8编码
      const BOM = '\uFEFF';
      const csvContent = BOM + template;

      // 首先尝试使用Tauri的文件对话框
      try {
        const filePath = await save({
          defaultPath: '人员授权导入模板.csv',
          filters: [{
            name: 'CSV文件',
            extensions: ['csv']
          }]
        });

        if (filePath) {
          await writeTextFile(filePath, csvContent);
          console.log('文件保存成功:', filePath);
          return;
        }
      } catch (tauriError) {
        console.log('Tauri文件保存失败，尝试浏览器下载:', tauriError);
      }

      // 备用方法：浏览器下载
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      console.log('创建Blob成功，大小:', blob.size);

      // 尝试多种下载方法
      if (navigator.userAgent.includes('Safari') && !navigator.userAgent.includes('Chrome')) {
        // Safari特殊处理
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = '人员授权导入模板.csv';
        link.target = '_blank';

        // 强制触发下载
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        setTimeout(() => URL.revokeObjectURL(url), 100);
      } else {
        // 标准方法
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');

        link.setAttribute('href', url);
        link.setAttribute('download', '人员授权导入模板.csv');
        link.style.display = 'none';

        document.body.appendChild(link);
        console.log('触发下载...');

        // 使用setTimeout确保DOM更新
        setTimeout(() => {
          link.click();
          document.body.removeChild(link);
          URL.revokeObjectURL(url);
          console.log('下载完成');
        }, 10);
      }
    } catch (error: any) {
      console.error('下载模板失败:', error);
      throw new Error(`下载模板失败: ${error}`);
    }
  }
}

// 创建服务实例
export const csvImportService = new CsvImportService();
